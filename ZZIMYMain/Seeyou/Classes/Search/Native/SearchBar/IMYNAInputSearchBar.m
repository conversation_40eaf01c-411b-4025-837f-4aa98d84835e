//
//  IMYNAInputSearchBar.m
//  ZZIMYMain
//
//  Created by ljh on 2024/4/17.
//

#import "IMYNAInputSearchBar.h"
#import "IMYNASearchHistoryModel.h"
#import <IMYBaseKit/IMYGradientTextLabel.h>
#import <IMYCommonKit/IMYSearchTagHelper.h>

@interface IMYNAInputSearchBar () <UITextFieldDelegate>

// 实验组配置
@property (nonatomic, assign) NSInteger experimentGroup;

// 8.93.0版本：搜索词图标标签
@property (nonatomic, strong) UIView *iconTagView; // 图标视图

@end


@implementation IMYNAInputSearchBar

- (instancetype)initWithFrame:(CGRect)frame {
    frame.size = CGSizeMake(375, 44);
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
        [self setupStyles];
    }
    return self;
}

- (void)setupSubviews {
    self.allBGView = [[UIView alloc] initWithFrame:CGRectMake(0, -SCREEN_STATUSBAR_HEIGHT, 375, 44 + SCREEN_STATUSBAR_HEIGHT)];
    self.allBGView.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    [self addSubview:self.allBGView];
    
    self.searchBGView = [[UIView alloc] initWithFrame:CGRectMake(42, 4, 375 - 42 - 50, 36)];
    self.searchBGView.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    [self addSubview:self.searchBGView];
    
    self.textField = [[UITextField alloc] initWithFrame:CGRectMake(42 + 32, 4, 375 - 42 - 50 - 32 - 5, 36)];
    self.textField.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
    self.textField.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    self.textField.returnKeyType = UIReturnKeySearch;
    self.textField.enablesReturnKeyAutomatically = YES;
    self.textField.clearButtonMode = UITextFieldViewModeWhileEditing;
    self.textField.autocapitalizationType = UITextAutocapitalizationTypeNone;
    self.textField.delegate = self;
    [self addSubview:self.textField];
    
    self.searchIcon = [[UIImageView alloc] initWithFrame:CGRectMake(42 + 12, 14, 16, 16)];
    self.searchIcon.autoresizingMask = UIViewAutoresizingFlexibleRightMargin;
    [self addSubview:self.searchIcon];
    
    self.leftButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(12, 4, 30, 36)];
    self.leftButton.autoresizingMask = UIViewAutoresizingFlexibleRightMargin;
    self.leftButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
    self.leftButton.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];//
    [self.leftButton addTarget:self action:@selector(leftButtonDo:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.leftButton];
    
    self.rightButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(375 - 36 - 10, 4, 36, 36)];
    self.rightButton.autoresizingMask = UIViewAutoresizingFlexibleLeftMargin;
    self.rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
    self.rightButton.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
    [self.rightButton addTarget:self action:@selector(rightButtonDo:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.rightButton];
}

- (void)usingHideLeftButtonStyle {
    // 老样式
    self.textField.clearButtonMode = UITextFieldViewModeWhileEditing;
    
    [self.leftButton removeFromSuperview];
    [self.rightButton imy_setTitle:IMYString(@"取消")];
    
    self.searchBGView.imy_left = 16;
    self.searchBGView.imy_width = 375 - 16 - 50;
    
    self.textField.imy_left = 16 + 32;
    self.textField.imy_width = 375 - 16 - 50 - 32 - 5;
    
    self.searchIcon.imy_left = 16 + 12;
}

- (void)setupWithExperimentGroup:(NSInteger)experimentGroup {
    self.experimentGroup = experimentGroup;
    
    if (experimentGroup == 1 || experimentGroup == 2) {
        // 实验组1和2：左侧返回按钮 + 右侧搜索按钮
        self.textField.clearButtonMode = UITextFieldViewModeWhileEditing;
        
        // 确保左按钮存在且可见
        if (!self.leftButton.superview) {
            [self addSubview:self.leftButton];
        }
        
        // 设置右按钮为搜索
        [self.rightButton imy_setTitle:IMYString(@"搜索")];
        
        // 根据实验组设置搜索按钮颜色
        if (experimentGroup == 1) {
            // 实验组1：黑色搜索按钮
            [self.rightButton imy_setTitleColor:kCK_Black_A];
        } else if (experimentGroup == 2) {
            // 实验组2：线性渐变搜索按钮
            [self setupGradientTextForButton:self.rightButton];
        }
        
        // 恢复默认布局
        self.searchBGView.imy_left = 42;
        self.searchBGView.imy_width = 375 - 42 - 50;
        
        self.textField.imy_left = 42 + 32;
        self.textField.imy_width = 375 - 42 - 50 - 32 - 5;
        
        self.searchIcon.imy_left = 42 + 12;
    }
}

- (void)setupGradientTextForButton:(UIButton *)button {
    // 移除之前可能添加的自定义View
    for (UIView *subview in button.subviews) {
        if ([subview isKindOfClass:[IMYGradientTextLabel class]]) {
            [subview removeFromSuperview];
        }
    }
    [button setTitleColor:[UIColor clearColor] forState:UIControlStateNormal];
    IMYGradientTextLabel *textLabel = [[IMYGradientTextLabel alloc] initWithFont:[UIFont systemFontOfSize:15] colors:@[[UIColor imy_colorForKey:@"#5988FE"],[UIColor imy_colorForKey:@"#B54EFF"]]];
    textLabel.userInteractionEnabled = NO;
    textLabel.text = button.titleLabel.text;
   
    [self setNeedsLayout];
    [self layoutIfNeeded];
    [button addSubview:textLabel];
    textLabel.frame = button.titleLabel.frame;
}


- (void)hideSearchSubviews {
    self.searchBGView.alpha = 0.01;
    self.searchIcon.alpha = 0.01;
    self.textField.alpha = 0.01;
}

- (void)showSearchSubviews {
    self.searchBGView.alpha = 1;
    self.searchIcon.alpha = 1;
    self.textField.alpha = 1;
}

- (void)setupStyles {
    [self imy_setBackgroundColorForKey:kIMY_BG];
    
    [self.allBGView imy_setBackgroundColorForKey:kIMY_BG];
    
    self.textField.placeholder = IMYString(@"请输入关键字");
    self.textField.tintColor = [UIColor imy_colorForKey:kCK_Red_B];
    self.textField.backgroundColor = [UIColor clearColor];
    [self.textField imy_setClearButtonStyle:2];
    
    [self.searchBGView imy_setBackgroundColorForKey:kCK_Black_HNS];
    [self.searchBGView imy_drawAllCornerRadius:18];

    [self.searchIcon imy_setImage:@"searchicon_v2"];

    [self.rightButton imy_setTitle:IMYString(@"搜索")];
    [self.rightButton imy_setTitleColor:kCK_Black_A];

    [self.leftButton imy_addThemeChangedBlock:^(UIButton *weakObject) {
        UIImage *image = nil;
        if (![IMYPublicAppHelper shareAppHelper].isNight) {
            image = [UIImage imy_imageForKey:@"nav_btn_back_black"];
        } else {
            image = [UIImage imy_imageForKey:@"nav_btn_back"];
        }
        [weakObject setImage:image forState:UIControlStateNormal];
    }];
    
    self.textField.delegate = self;
    [self.textField imy_setTextColorForKey:kCK_Black_A];
    [self.textField imy_setPlaceholderColorForKey:kCK_Black_B];
}

- (void)textFieldDidBeginEditing:(UITextField *)textField {
    
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    if (self.onTextFieldReturnBlock) {
        self.onTextFieldReturnBlock(textField);
    }
    return YES;
}

- (BOOL)textFieldShouldClear:(UITextField *)textField {
    if (self.onTextFieldClearBlock) {
        self.onTextFieldClearBlock(textField);
    }
    return YES;
}

- (void)leftButtonDo:(id)sender {
    if (_onLeftButtonClick) {
        _onLeftButtonClick();
    } else {
        [self.textField resignFirstResponder];
        [[self imy_viewController] imy_pop:YES];
    }
}

- (void)rightButtonDo:(id)sender {
    if (self.experimentGroup == 1 || self.experimentGroup == 2) {
        // 实验组1和2：右按钮是搜索功能，不收起键盘
        if (_onRightButtonClick) {
            _onRightButtonClick();
        }
    } else {
        // 其他情况：右按钮行为由外部回调决定，默认收起键盘
        [self.textField resignFirstResponder];
        if (_onRightButtonClick) {
            _onRightButtonClick();
        }
    }
}

#pragma mark - 8.93.0版本：图标标签相关方法

/// 根据icon_type创建对应的图标标签
- (UIView *)createIconTagWithType:(IMYSearchIconType)iconType {
    return [IMYSearchTagHelper createTagWithType:iconType];
}

/// 更新图标标签显示
- (void)updateIconTagWithType:(NSInteger)iconType {
    IMYSearchIconType searchIconType = (IMYSearchIconType)iconType;

    // 检查是否需要更新图标
    BOOL shouldShowIcon = (searchIconType == IMYSearchIconTypeHot ||
                          searchIconType == IMYSearchIconTypeRecommend ||
                          searchIconType == IMYSearchIconTypeNew ||
                          searchIconType == IMYSearchIconTypeAI);
    BOOL hasIcon = (self.iconTagView != nil);

    if (!shouldShowIcon && hasIcon) {
        // 需要移除图标
        [self.iconTagView removeFromSuperview];
        self.iconTagView = nil;
    } else if (shouldShowIcon && !hasIcon) {
        // 需要创建图标
        self.iconTagView = [self createIconTagWithType:searchIconType];
        if (self.iconTagView) {
            // 将图标标签添加到textField上，这样可以跟随placeholder文本
            [self.textField addSubview:self.iconTagView];

            // 动态计算图标位置，紧贴placeholder文本右侧
            [self updateIconTagPosition];
        }
    } else if (shouldShowIcon && hasIcon) {
        // 图标已存在，只更新位置
        [self updateIconTagPosition];
    }
    // 如果 !shouldShowIcon && !hasIcon，则无需任何操作
}

/// 动态更新图标标签位置，紧贴placeholder文本右侧
- (void)updateIconTagPosition {
    if (!self.iconTagView || !self.textField.placeholder) {
        return;
    }

    // 计算当前placeholder文本的实际显示宽度
    NSString *placeholder = self.textField.placeholder;
    UIFont *font = self.textField.font;
    NSDictionary *attributes = @{NSFontAttributeName: font};
    CGSize textSize = [placeholder boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, self.textField.frame.size.height)
                                                options:NSStringDrawingUsesLineFragmentOrigin
                                             attributes:attributes
                                                context:nil].size;

    // 设置图标位置：紧贴placeholder文本右侧，间距4px
    CGFloat iconX = textSize.width + 4;
    CGFloat iconY = (self.textField.frame.size.height - self.iconTagView.frame.size.height) / 2.0;

    self.iconTagView.frame = CGRectMake(iconX, iconY,
                                       self.iconTagView.frame.size.width,
                                       self.iconTagView.frame.size.height);
}

@end


@interface IMYNAInputAnimationView ()
// 8.93.0版本：搜索词图标标签
@property (nonatomic, strong) UIView *iconTagView; // 图标视图
@end

@implementation IMYNAInputAnimationView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.userInteractionEnabled = NO;
        self.autoresizingMask = UIViewAutoresizingNone;
        self.frame = CGRectMake(0, 0, 283, 36);
        [self imy_setBackgroundColorForKey:kCK_Black_HNS];
        [self imy_drawAllCornerRadius:18];

        self.textLabel = [[UILabel alloc] initWithFrame:CGRectMake(32, 0, 283 - 32 - 5, 36)];
        self.textLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        self.textLabel.autoresizingMask = UIViewAutoresizingFlexibleWidth;
        [self.textLabel imy_setTextColor:kCK_Black_B];
        [self addSubview:self.textLabel];

        self.searchIcon = [[UIImageView alloc] initWithFrame:CGRectMake(12, 10, 16, 16)];
        self.searchIcon.autoresizingMask = UIViewAutoresizingFlexibleRightMargin;
        [self.searchIcon imy_setImage:@"searchicon_v2"];
        [self addSubview:self.searchIcon];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];

    // 8.93.0版本：布局变化后更新图标位置
    [self updateIconTagPosition];
}

#pragma mark - 8.93.0版本：图标标签相关方法

/// 根据icon_type创建对应的图标标签
- (UIView *)createIconTagWithType:(IMYSearchIconType)iconType {
    return [IMYSearchTagHelper createTagWithType:iconType];
}

/// 更新图标标签显示（优化版本）
- (void)updateIconTagWithType:(NSInteger)iconType {
    // 检查是否需要更新图标
    BOOL shouldShowIcon = (iconType == 1 || iconType == 2 || iconType == 3 || iconType == 6);
    BOOL hasIcon = (self.iconTagView != nil);

    if (!shouldShowIcon && hasIcon) {
        // 需要移除图标
        [self.iconTagView removeFromSuperview];
        self.iconTagView = nil;
    } else if (shouldShowIcon && !hasIcon) {
        // 需要创建图标
        self.iconTagView = [self createIconTagWithType:iconType];
        if (self.iconTagView) {
            // 将图标标签添加到textLabel上，这样可以跟随文本
            [self.textLabel addSubview:self.iconTagView];

            // 动态计算图标位置，紧贴文本右侧
            [self updateIconTagPosition];
        }
    } else if (shouldShowIcon && hasIcon) {
        // 图标已存在，检查是否需要更新类型
        [self.iconTagView removeFromSuperview];
        self.iconTagView = [self createIconTagWithType:iconType];
        if (self.iconTagView) {
            [self.textLabel addSubview:self.iconTagView];
            [self updateIconTagPosition];
        }
    }
    // 如果 !shouldShowIcon && !hasIcon，则无需任何操作
}

/// 动态更新图标标签位置，紧贴文本右侧
- (void)updateIconTagPosition {
    if (!self.iconTagView || !self.textLabel.text) {
        return;
    }

    // 计算当前文本的实际显示宽度
    NSString *text = self.textLabel.text;
    UIFont *font = self.textLabel.font;
    NSDictionary *attributes = @{NSFontAttributeName: font};
    CGSize textSize = [text boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, self.textLabel.frame.size.height)
                                         options:NSStringDrawingUsesLineFragmentOrigin
                                      attributes:attributes
                                         context:nil].size;

    // 设置图标位置：紧贴文本右侧，间距4px
    CGFloat iconX = textSize.width + 4;
    CGFloat iconY = (self.textLabel.frame.size.height - self.iconTagView.frame.size.height) / 2.0;

    // 确保图标不会超出textLabel的边界
    CGFloat maxX = self.textLabel.frame.size.width - self.iconTagView.frame.size.width;
    if (iconX > maxX) {
        iconX = maxX;
    }

    self.iconTagView.frame = CGRectMake(iconX, iconY,
                                       self.iconTagView.frame.size.width,
                                       self.iconTagView.frame.size.height);
}

@end




